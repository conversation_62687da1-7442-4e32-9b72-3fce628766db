# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "e71dd56cce27b063fd9452f7c3856aaa"
application_url = "https://example.com/"
embedded = true
name = "Report Hub"
handle = "report-hub"

[build]
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = [ "https://example.com/api/auth" ]

[pos]
embedded = false
